<?php

/**
 * Test script to demonstrate the Scholarship Edit fix
 * 
 * This script shows how to test the scholarship edit functionality
 * with document requirements to ensure the fix is working properly.
 * 
 * Usage: php test_scholarship_edit.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\Admin\ScholarshipController;
use App\Models\ScholarshipProgram;
use App\Models\DocumentRequirement;
use App\Models\User;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Scholarship Edit Fix Test ===\n\n";

try {
    // Create a test admin user
    $admin = User::factory()->create([
        'email' => '<EMAIL>',
        'role' => 'admin',
        'name' => 'Test Admin'
    ]);
    
    echo "✓ Created test admin user: {$admin->email}\n";

    // Create a test scholarship with some document requirements
    $scholarship = ScholarshipProgram::factory()->create([
        'name' => 'Test Scholarship for Edit',
        'description' => 'Testing scholarship edit functionality',
    ]);
    
    echo "✓ Created test scholarship: {$scholarship->name} (ID: {$scholarship->id})\n";

    // Add some initial document requirements
    $req1 = DocumentRequirement::create([
        'scholarship_program_id' => $scholarship->id,
        'name' => 'Original Requirement 1',
        'description' => 'This is the first original requirement',
        'is_required' => true,
    ]);
    
    $req2 = DocumentRequirement::create([
        'scholarship_program_id' => $scholarship->id,
        'name' => 'Original Requirement 2',
        'description' => 'This is the second original requirement',
        'is_required' => false,
    ]);
    
    echo "✓ Created initial document requirements:\n";
    echo "  - {$req1->name} (ID: {$req1->id})\n";
    echo "  - {$req2->name} (ID: {$req2->id})\n";

    // Simulate the frontend request data for updating the scholarship
    $updateData = [
        'name' => $scholarship->name,
        'description' => $scholarship->description,
        'total_budget' => $scholarship->total_budget,
        'per_student_budget' => $scholarship->per_student_budget,
        'school_type_eligibility' => $scholarship->school_type_eligibility,
        'min_gpa' => $scholarship->min_gpa,
        'min_units' => $scholarship->min_units,
        'semester' => $scholarship->semester,
        'academic_year' => $scholarship->academic_year,
        'application_deadline' => $scholarship->application_deadline->format('Y-m-d'),
        'community_service_days' => $scholarship->community_service_days,
        'active' => $scholarship->active,
        'available_slots' => $scholarship->available_slots,
        'documentRequirements' => [
            // Update existing requirement 1
            [
                'id' => $req1->id,
                'name' => 'Updated Requirement 1',
                'description' => 'This requirement has been updated',
                'is_required' => true,
                'isNew' => false,
                'isDeleted' => false,
            ],
            // Mark requirement 2 for deletion
            [
                'id' => $req2->id,
                'name' => $req2->name,
                'description' => $req2->description,
                'is_required' => $req2->is_required,
                'isNew' => false,
                'isDeleted' => true,
            ],
            // Add new requirement
            [
                'id' => null,
                'name' => 'Brand New Requirement',
                'description' => 'This is a completely new requirement',
                'is_required' => true,
                'isNew' => true,
                'isDeleted' => false,
            ],
        ],
    ];

    echo "\n=== Testing Update Process ===\n";
    echo "Simulating frontend update request with:\n";
    echo "- Update existing requirement 1\n";
    echo "- Delete existing requirement 2\n";
    echo "- Add new requirement\n\n";

    // Create a mock request
    $request = Request::create('/admin/scholarships/' . $scholarship->id, 'PUT', $updateData);
    
    // Authenticate as admin
    auth()->login($admin);
    
    // Create controller instance and call update method
    $controller = new ScholarshipController();
    
    echo "Calling controller update method...\n";
    $response = $controller->update($request, $scholarship);
    
    echo "✓ Update method completed successfully\n\n";

    // Verify the results
    $scholarship->refresh();
    $updatedRequirements = $scholarship->documentRequirements()->get();
    
    echo "=== Verification Results ===\n";
    echo "Final document requirements count: " . $updatedRequirements->count() . "\n\n";
    
    foreach ($updatedRequirements as $req) {
        echo "✓ Requirement: {$req->name} (ID: {$req->id})\n";
        echo "  Description: {$req->description}\n";
        echo "  Required: " . ($req->is_required ? 'Yes' : 'No') . "\n\n";
    }

    // Check specific expectations
    $hasUpdatedReq1 = $updatedRequirements->where('name', 'Updated Requirement 1')->isNotEmpty();
    $hasDeletedReq2 = $updatedRequirements->where('id', $req2->id)->isNotEmpty();
    $hasNewReq = $updatedRequirements->where('name', 'Brand New Requirement')->isNotEmpty();
    
    echo "=== Test Results ===\n";
    echo ($hasUpdatedReq1 ? "✓" : "✗") . " Requirement 1 was updated\n";
    echo ($hasDeletedReq2 ? "✗" : "✓") . " Requirement 2 was deleted\n";
    echo ($hasNewReq ? "✓" : "✗") . " New requirement was added\n";
    
    if ($hasUpdatedReq1 && !$hasDeletedReq2 && $hasNewReq) {
        echo "\n🎉 ALL TESTS PASSED! The scholarship edit fix is working correctly.\n";
    } else {
        echo "\n❌ Some tests failed. Please check the implementation.\n";
    }

} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} finally {
    // Clean up test data
    if (isset($scholarship)) {
        $scholarship->documentRequirements()->delete();
        $scholarship->delete();
        echo "\n✓ Cleaned up test scholarship\n";
    }
    
    if (isset($admin)) {
        $admin->delete();
        echo "✓ Cleaned up test admin user\n";
    }
}

echo "\n=== Test Complete ===\n";
